from flask import Flask, render_template, Response, jsonify
import cv2
import torch
import numpy as np
from ultralytics import YOLO
import time
import threading
import json

app = Flask(__name__)

class SIBIWebDetector:
    def __init__(self, model_path='models/sibiv3.pt'):
        """
        Initialize SIBI detector for web interface
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # Initialize camera and model
        self.cap = None
        self.model = None
        self.current_prediction = None
        self.current_confidence = 0.0
        self.is_running = False
        
        # Load the model
        try:
            self.model = YOLO(model_path)
            print("Model loaded successfully!")
        except Exception as e:
            print(f"Error loading model: {e}")
            raise Exception(f"Failed to load model: {e}")
        
        # Initialize camera
        self.cap = cv2.VideoCapture(0)
        if not self.cap.isOpened():
            print("Error: Could not open camera")
            raise Exception("Failed to open camera")
        
        # Set camera properties
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        print("Camera initialized successfully!")
        
        # Detection parameters
        self.confidence_threshold = 0.5
        self.prediction_history = []
        self.history_size = 5
        
    def predict(self, frame):
        """
        Make prediction on the frame using YOLO
        """
        try:
            results = self.model(frame, verbose=False)
            
            if len(results) > 0 and len(results[0].boxes) > 0:
                # Get the best detection
                boxes = results[0].boxes
                confidences = boxes.conf.cpu().numpy()
                classes = boxes.cls.cpu().numpy()
                
                # Get highest confidence detection
                best_idx = confidences.argmax()
                predicted = int(classes[best_idx])
                confidence = float(confidences[best_idx])
                
                # Get bounding box
                bbox = boxes.xyxy[best_idx].cpu().numpy()
                
                return predicted, confidence, bbox
            else:
                return None, 0.0, None
        
        except Exception as e:
            print(f"Prediction error: {e}")
            return None, 0.0, None
    
    def smooth_predictions(self, prediction, confidence):
        """
        Smooth predictions using history to reduce noise
        """
        if confidence > self.confidence_threshold:
            self.prediction_history.append(prediction)
        
        # Keep only recent predictions
        if len(self.prediction_history) > self.history_size:
            self.prediction_history.pop(0)
        
        # Return most common prediction if we have enough history
        if len(self.prediction_history) >= 3:
            most_common = max(set(self.prediction_history), 
                            key=self.prediction_history.count)
            return most_common
        
        return prediction if confidence > self.confidence_threshold else None
    
    def draw_info(self, frame, prediction, confidence, bbox=None):
        """
        Draw prediction information on frame
        """
        height, width = frame.shape[:2]
        
        # Draw bounding box if available
        if bbox is not None:
            x1, y1, x2, y2 = bbox.astype(int)
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # Draw prediction info
        if prediction is not None and confidence > self.confidence_threshold:
            # Get class name from model
            class_names = self.model.names
            label = class_names.get(prediction, f"Class_{prediction}")
            
            # Background for text
            cv2.rectangle(frame, (10, 10), (400, 100), (0, 0, 0), -1)
            
            # Prediction text
            cv2.putText(frame, f"Prediction: {label}", 
                       (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Confidence: {confidence:.2f}", 
                       (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Update current prediction for API
            self.current_prediction = label
            self.current_confidence = confidence
        else:
            cv2.rectangle(frame, (10, 10), (300, 60), (0, 0, 0), -1)
            cv2.putText(frame, "No detection", 
                       (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            # Clear current prediction
            self.current_prediction = None
            self.current_confidence = 0.0
        
        return frame
    
    def generate_frames(self):
        """
        Generate frames for video streaming
        """
        self.is_running = True
        
        while self.is_running:
            ret, frame = self.cap.read()
            if not ret:
                break
            
            # Flip frame horizontally for mirror effect
            frame = cv2.flip(frame, 1)
            
            # Make prediction
            prediction, confidence, bbox = self.predict(frame)
            smoothed_prediction = self.smooth_predictions(prediction, confidence)
            
            # Draw information on frame
            annotated_frame = self.draw_info(frame, smoothed_prediction, confidence, bbox)
            
            # Encode frame as JPEG
            ret, buffer = cv2.imencode('.jpg', annotated_frame)
            if ret:
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
    
    def stop(self):
        """
        Stop the detector
        """
        self.is_running = False
        if self.cap is not None:
            self.cap.release()

# Global detector instance
detector = None

@app.route('/')
def index():
    """
    Main page
    """
    return render_template('index.html')

@app.route('/video_feed')
def video_feed():
    """
    Video streaming route
    """
    global detector
    if detector is None:
        try:
            detector = SIBIWebDetector()
        except Exception as e:
            return f"Error initializing detector: {e}", 500
    
    return Response(detector.generate_frames(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/api/prediction')
def get_prediction():
    """
    API endpoint to get current prediction
    """
    global detector
    if detector is None:
        return jsonify({'error': 'Detector not initialized'}), 500
    
    return jsonify({
        'prediction': detector.current_prediction,
        'confidence': detector.current_confidence,
        'timestamp': time.time()
    })

@app.route('/api/stop')
def stop_detector():
    """
    API endpoint to stop the detector
    """
    global detector
    if detector is not None:
        detector.stop()
        detector = None
    return jsonify({'status': 'stopped'})

if __name__ == '__main__':
    # Create templates directory and HTML file
    import os
    os.makedirs('templates', exist_ok=True)
    
    html_content = '''
<!DOCTYPE html>
<html>
<head>
    <title>SIBI Real-time Detector</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .video-container { text-align: center; margin: 20px 0; }
        .prediction-info { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .no-detection { background: #ffe8e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #prediction-display { font-size: 18px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SIBI Real-time Sign Language Detector</h1>
        <p>Position your hand in front of the camera to detect SIBI sign language.</p>
        
        <div class="video-container">
            <img src="{{ url_for('video_feed') }}" width="640" height="480" style="border: 2px solid #ccc; border-radius: 10px;">
        </div>
        
        <div id="prediction-info" class="no-detection">
            <div id="prediction-display">No detection</div>
            <div id="confidence-display"></div>
        </div>
        
        <div style="text-align: center;">
            <button onclick="stopDetector()">Stop Detector</button>
        </div>
    </div>

    <script>
        // Update prediction info every 500ms
        setInterval(function() {
            fetch('/api/prediction')
                .then(response => response.json())
                .then(data => {
                    const predictionDiv = document.getElementById('prediction-info');
                    const predictionDisplay = document.getElementById('prediction-display');
                    const confidenceDisplay = document.getElementById('confidence-display');
                    
                    if (data.prediction && data.confidence > 0.5) {
                        predictionDiv.className = 'prediction-info';
                        predictionDisplay.textContent = `Prediction: ${data.prediction}`;
                        confidenceDisplay.textContent = `Confidence: ${(data.confidence * 100).toFixed(1)}%`;
                    } else {
                        predictionDiv.className = 'no-detection';
                        predictionDisplay.textContent = 'No detection';
                        confidenceDisplay.textContent = '';
                    }
                })
                .catch(error => console.error('Error:', error));
        }, 500);
        
        function stopDetector() {
            fetch('/api/stop')
                .then(response => response.json())
                .then(data => {
                    alert('Detector stopped');
                    location.reload();
                });
        }
    </script>
</body>
</html>
    '''
    
    with open('templates/index.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("Starting SIBI Web Detector...")
    print("Open your browser and go to: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=False)
