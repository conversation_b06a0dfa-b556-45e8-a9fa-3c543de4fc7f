
<!DOCTYPE html>
<html>
<head>
    <title>SIBI Real-time Detector</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .video-container { text-align: center; margin: 20px 0; }
        .prediction-info { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .no-detection { background: #ffe8e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #prediction-display { font-size: 18px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SIBI Real-time Sign Language Detector</h1>
        <p>Position your hand in front of the camera to detect SIBI sign language.</p>
        
        <div class="video-container">
            <img src="{{ url_for('video_feed') }}" width="640" height="480" style="border: 2px solid #ccc; border-radius: 10px;">
        </div>
        
        <div id="prediction-info" class="no-detection">
            <div id="prediction-display">No detection</div>
            <div id="confidence-display"></div>
        </div>
        
        <div style="text-align: center;">
            <button onclick="stopDetector()">Stop Detector</button>
        </div>
    </div>

    <script>
        // Update prediction info every 500ms
        setInterval(function() {
            fetch('/api/prediction')
                .then(response => response.json())
                .then(data => {
                    const predictionDiv = document.getElementById('prediction-info');
                    const predictionDisplay = document.getElementById('prediction-display');
                    const confidenceDisplay = document.getElementById('confidence-display');
                    
                    if (data.prediction && data.confidence > 0.5) {
                        predictionDiv.className = 'prediction-info';
                        predictionDisplay.textContent = `Prediction: ${data.prediction}`;
                        confidenceDisplay.textContent = `Confidence: ${(data.confidence * 100).toFixed(1)}%`;
                    } else {
                        predictionDiv.className = 'no-detection';
                        predictionDisplay.textContent = 'No detection';
                        confidenceDisplay.textContent = '';
                    }
                })
                .catch(error => console.error('Error:', error));
        }, 500);
        
        function stopDetector() {
            fetch('/api/stop')
                .then(response => response.json())
                .then(data => {
                    alert('Detector stopped');
                    location.reload();
                });
        }
    </script>
</body>
</html>
    