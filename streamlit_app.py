import streamlit as st
import cv2
import torch
import numpy as np
from ultralytics import YOLO
import time
from PIL import Image
import tempfile
import os

# Page config
st.set_page_config(
    page_title="SIBI Real-time Sign Language Detector",
    page_icon="🤟",
    layout="wide",
    initial_sidebar_state="expanded"
)

class SIBIStreamlitDetector:
    def __init__(self, model_path='models/sibiv3.pt'):
        """
        Initialize SIBI detector for Streamlit (same as Flask version)
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Initialize camera and model
        self.cap = None
        self.model = None
        self.current_prediction = None
        self.current_confidence = 0.0
        self.is_running = False

        # Load the model
        try:
            self.model = YOLO(model_path)
        except Exception as e:
            st.error(f"Error loading model: {e}")
            raise Exception(f"Failed to load model: {e}")

        # Detection parameters (same as Flask version)
        self.confidence_threshold = 0.5
        self.prediction_history = []
        self.history_size = 5

    def start_camera(self):
        """Start camera capture (same as Flask version)"""
        if self.cap is None:
            self.cap = cv2.VideoCapture(0)
            if not self.cap.isOpened():
                raise Exception("Could not open camera")
        self.is_running = True

    def stop_camera(self):
        """Stop camera capture (same as Flask version)"""
        self.is_running = False
        if self.cap:
            self.cap.release()
            self.cap = None
        
    def predict(self, frame):
        """
        Make prediction on the frame using YOLO (same as Flask version)
        """
        try:
            results = self.model(frame, verbose=False)
            
            if len(results) > 0 and len(results[0].boxes) > 0:
                # Get the best detection
                boxes = results[0].boxes
                confidences = boxes.conf.cpu().numpy()
                classes = boxes.cls.cpu().numpy()
                
                # Get highest confidence detection
                best_idx = confidences.argmax()
                predicted = int(classes[best_idx])
                confidence = float(confidences[best_idx])
                
                # Get bounding box
                bbox = boxes.xyxy[best_idx].cpu().numpy()
                
                return predicted, confidence, bbox
            else:
                return None, 0.0, None
        
        except Exception as e:
            st.error(f"Prediction error: {e}")
            return None, 0.0, None
    
    def smooth_predictions(self, prediction, confidence):
        """
        Smooth predictions using history to reduce noise (same as Flask version)
        """
        if confidence > self.confidence_threshold:
            self.prediction_history.append(prediction)
        
        # Keep only recent predictions
        if len(self.prediction_history) > self.history_size:
            self.prediction_history.pop(0)
        
        # Return most common prediction if we have enough history
        if len(self.prediction_history) >= 3:
            most_common = max(set(self.prediction_history), 
                            key=self.prediction_history.count)
            return most_common
        
        return prediction if confidence > self.confidence_threshold else None
    
    def draw_info(self, frame, prediction, confidence, bbox=None):
        """
        Draw prediction information on frame (same as Flask version)
        """
        
        # Draw bounding box if available
        if bbox is not None:
            x1, y1, x2, y2 = bbox.astype(int)
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # Draw prediction info
        if prediction is not None and confidence > self.confidence_threshold:
            # Get class name from model
            class_names = self.model.names
            label = class_names.get(prediction, f"Class_{prediction}")
            
            # Background for text
            cv2.rectangle(frame, (10, 10), (400, 100), (0, 0, 0), -1)
            
            # Prediction text
            cv2.putText(frame, f"Prediction: {label}", 
                       (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Confidence: {confidence:.2f}", 
                       (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Update current prediction for display
            self.current_prediction = label
            self.current_confidence = confidence
        else:
            cv2.rectangle(frame, (10, 10), (300, 60), (0, 0, 0), -1)
            cv2.putText(frame, "No detection", 
                       (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            # Clear current prediction
            self.current_prediction = None
            self.current_confidence = 0.0
        
        return frame

@st.cache_resource
def load_detector():
    """Load detector with caching"""
    try:
        return SIBIStreamlitDetector()
    except Exception as e:
        st.error(f"Failed to initialize detector: {e}")
        return None

def process_image(detector, image_array, confidence_threshold):
    """Process image and return results"""
    # Convert RGB to BGR for OpenCV
    image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
    
    # Make prediction
    prediction, confidence, bbox = detector.predict(image_bgr)
    smoothed_prediction = detector.smooth_predictions(prediction, confidence)
    
    # Update confidence threshold
    detector.confidence_threshold = confidence_threshold
    
    # Draw information on frame
    annotated_frame = detector.draw_info(image_bgr, smoothed_prediction, confidence, bbox)
    
    # Convert back to RGB for display
    annotated_frame_rgb = cv2.cvtColor(annotated_frame, cv2.COLOR_BGR2RGB)
    
    return annotated_frame_rgb, smoothed_prediction, confidence

def main():
    st.title("🤟 SIBI Real-time Sign Language Detector")
    st.markdown("**Position your hand in front of the camera to detect SIBI sign language.**")

    # Sidebar (same functionality as Flask version)
    st.sidebar.header("Settings")
    confidence_threshold = st.sidebar.slider(
        "Confidence Threshold",
        min_value=0.0,
        max_value=1.0,
        value=0.5,
        step=0.05,
        help="Adjust detection sensitivity"
    )

    # Load detector
    detector = load_detector()
    if detector is None:
        st.stop()

    # Update confidence threshold
    detector.confidence_threshold = confidence_threshold

    # Main interface tabs
    tab1, tab2, tab3 = st.tabs(["📷 Real-time Detection", "📁 Upload Image", "ℹ️ About"])
    
    with tab1:
        st.header("Real-time Detection")
        st.markdown("**SIBI detection with camera input (optimized for Streamlit)**")

        # Settings in sidebar
        st.sidebar.markdown("### 🎥 Camera Settings")
        detection_mode = st.sidebar.radio(
            "Detection Mode:",
            ["Manual Capture", "Auto Refresh"],
            help="Manual = Click to capture, Auto = Continuous refresh"
        )

        if detection_mode == "Auto Refresh":
            refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 0.5, 3.0, 1.0, 0.1)
            st.sidebar.info("Lower interval = faster refresh but may cause flicker")

        # Camera input
        st.subheader("📷 Camera Feed")
        camera_input = st.camera_input(
            "Position your hand to show SIBI signs",
            key=f"camera_{int(time.time())}" if detection_mode == "Auto Refresh" else "camera_manual"
        )

        if camera_input is not None:
            # Process the captured image
            image = Image.open(camera_input)
            image_array = np.array(image)

            # Make prediction using detector
            result_image, prediction, confidence = process_image(
                detector, image_array, confidence_threshold
            )

            # Display results in columns
            col1, col2 = st.columns(2)

            with col1:
                st.subheader("📷 Original")
                st.image(image, use_container_width=True)

            with col2:
                st.subheader("🎯 Detection Result")
                st.image(result_image, use_container_width=True)

            # Prediction results
            st.subheader("📊 Detection Results")

            if prediction and confidence > confidence_threshold:
                # Success case
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.success(f"**Detected:** {prediction}")
                with col2:
                    st.info(f"**Confidence:** {confidence:.1%}")
                with col3:
                    st.metric("Score", f"{confidence:.1%}")

                # Progress bar
                st.progress(confidence, text=f"Confidence: {confidence:.1%}")

                # Detailed info
                with st.expander("📋 Detection Details"):
                    st.json({
                        'prediction': prediction,
                        'confidence': confidence,
                        'threshold': confidence_threshold,
                        'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                        'status': 'detected'
                    })
            else:
                # No detection case
                st.warning("👀 No SIBI sign detected above threshold")
                if prediction:
                    st.info(f"Low confidence detection: {prediction} ({confidence:.1%})")

                with st.expander("� Detection Details"):
                    st.json({
                        'prediction': prediction or 'None',
                        'confidence': confidence,
                        'threshold': confidence_threshold,
                        'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                        'status': 'below_threshold'
                    })

            # Auto refresh for continuous detection
            if detection_mode == "Auto Refresh":
                time.sleep(refresh_interval)
                st.rerun()
        else:
            # Instructions when no camera input
            st.info("👆 Click the camera button above to capture and detect SIBI signs")
            st.markdown("""
            **Instructions:**
            1. Click the camera button to open your camera
            2. Position your hand with a SIBI sign
            3. Take the photo
            4. View the detection results below

            **For continuous detection:** Select "Auto Refresh" mode in the sidebar.
            """)
    
    with tab2:
        st.header("Upload Image")
        st.markdown("Upload an image containing SIBI sign language")
        
        uploaded_file = st.file_uploader(
            "Choose an image file", 
            type=['png', 'jpg', 'jpeg'],
            help="Upload an image for SIBI detection"
        )
        
        if uploaded_file is not None:
            # Load and process image
            image = Image.open(uploaded_file)
            image_array = np.array(image)
            
            # Process image
            result_image, prediction, confidence = process_image(
                detector, image_array, confidence_threshold
            )
            
            # Display results
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Uploaded Image")
                st.image(image, use_container_width=True)

            with col2:
                st.subheader("Detection Result")
                st.image(result_image, use_container_width=True)
            
            # Results display
            if prediction and confidence > confidence_threshold:
                st.success(f"**Prediction:** {prediction}")
                st.info(f"**Confidence:** {confidence:.2%}")
                st.progress(confidence)
                
                # Download functionality
                result_pil = Image.fromarray(result_image)
                buf = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                result_pil.save(buf.name)
                
                with open(buf.name, 'rb') as f:
                    st.download_button(
                        label="📥 Download Result",
                        data=f.read(),
                        file_name=f"sibi_detection_{int(time.time())}.png",
                        mime="image/png"
                    )
                
                os.unlink(buf.name)
            else:
                st.warning("No detection above threshold")
                if prediction:
                    st.info(f"Low confidence: {prediction} ({confidence:.2%})")
    
    with tab3:
        st.header("About SIBI Detector")
        
        st.markdown("""
        ### 🤟 Sistem Isyarat Bahasa Indonesia (SIBI)
        
        This application provides the same functionality as the Flask web detector:
        - Real-time sign language detection
        - Image upload and analysis
        - Confidence scoring and smoothing
        - Bounding box visualization
        
        ### 🔧 Technology Stack
        - **Model**: Ultralytics YOLO (same as Flask version)
        - **Backend**: PyTorch for inference
        - **Frontend**: Streamlit (instead of Flask)
        - **Computer Vision**: OpenCV for image processing
        
        ### 📊 Features Maintained from Flask Version
        - Prediction smoothing with history
        - Confidence threshold adjustment
        - Real-time prediction API equivalent
        - Same model loading and inference logic
        - Identical image processing pipeline
        """)
        
        # Model information (same as Flask version)
        if detector and detector.model:
            st.subheader("Model Information")
            st.write(f"**Device**: {detector.device}")
            st.write(f"**Model Type**: Ultralytics YOLO")
            st.write(f"**Classes**: {len(detector.model.names)} classes")
            st.write(f"**Confidence Threshold**: {confidence_threshold}")
            
            # Show available classes
            with st.expander("View All Classes"):
                for idx, name in detector.model.names.items():
                    st.write(f"**{idx}**: {name}")

if __name__ == "__main__":
    main()
